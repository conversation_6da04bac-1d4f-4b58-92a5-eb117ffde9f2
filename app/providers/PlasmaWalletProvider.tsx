"use client";

import { ReactNode } from "react";
import { PrivyProvider } from "@privy-io/react-auth";
import { plasmaMainnetChain } from "./networkChains";

interface PlasmaWalletProviderProps {
  children: ReactNode;
}

const PLASMA_PRIVY_APP_ID =
  process.env.NEXT_PUBLIC_PLASMA_PRIVY_APP_ID || "cmcvqsfzm0165jl0ms1i81h70";

export const PlasmaWalletProvider = ({
  children,
}: PlasmaWalletProviderProps) => {
  return (
    <PrivyProvider
      appId={PLASMA_PRIVY_APP_ID}
      config={{
        loginMethods: ["wallet", "email", "google", "twitter"],
        embeddedWallets: {
          ethereum: {
            createOnLogin: "users-without-wallets",
          },
        },
        appearance: {
          walletChainType: "ethereum-only",
          theme: "dark",
          accentColor: "#676FFF",
        },
        defaultChain: plasmaMainnet<PERSON>hain,
        supportedChains: [plasmaMainnetChain],
      }}
    >
      {children}
    </PrivyProvider>
  );
};
