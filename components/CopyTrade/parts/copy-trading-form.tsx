import clsx from "clsx";
import * as React from "react";
import { useEffect, useMemo, useRef, useState } from "react";
import { NumericFormat } from "react-number-format";
import { useDispatch, useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import { ChevronDownIcon, WalletIcon } from "@/assets/icons";
import { AppLogoNetwork, AppToggle } from "@/components";
import { useRaidenxWallet } from "@/hooks/useRaidenxWallet";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { toastError, toastSuccess } from "@/libs/toast";
import { ModalBottomMobile } from "@/modals";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import {
  setIsShowModalAddWallet,
  setIsShowModalEnableTradingPrivy,
} from "@/store/metadata.store";
import {
  EBuyStrategy,
  ESellStrategy,
  ISettingsBuy,
} from "@/types/copytrade.type";
import { formatNumber, formatNumberWithCommas } from "@/utils/format";
import { MENU_COPY_BUY } from "./constant";
import { FooterForm } from "./footer-form";
import { SettingsForm } from "./settings-form";
import { WalletSelection } from "./wallet-selection";
import { ModalAutoSell } from "@/components/OrderForm/buy-order/AutoSellForm";
import { RootPairContext } from "@/app/[network]/(token_detail)/provider";
import { TPair } from "@/types";
import { useSearchParams } from "next/navigation";
import { useNetwork } from "@/context/network";
import { usePrivyTradingEnablement } from "@/hooks/usePrivyTradingEnablement";
// import { ModalAutoSell } from "@/pages/pool/components/OrderForm/buy-order/AutoSellForm";

const CopyTradingForm = () => {
  const [copyBuy, setCopyBuy] = useState<EBuyStrategy>(
    EBuyStrategy.BUY_FIXED_AMOUNT
  );
  const copySell: ESellStrategy = ESellStrategy.SELL_PERCENT_AMOUNT;
  const dispatch = useDispatch();
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const [isShowSettings, setIsShowSettings] = useState<boolean>(false);
  const [isShowSelectWallet, setIsShowSelectWallet] = useState<boolean>(false);
  const [isShowModalWalletSelection, setShowModalWalletSelection] =
    useState<boolean>(false);
  const [isShowModalSettingsOrder, setShowModalSettingsOrder] =
    useState<boolean>(false);
  const { activeWallets } = useRaidenxWallet();
  const isTabletOrMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const [walletName, setWalletName] = useState<any>("");

  const [settingsBuy, setSettingsBuy] = useState<ISettingsBuy>({});
  const [targetTokenAddress, setTargetTokenAddress] = useState<any>("");
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const [enableCopyBuy, setEnableCopyBuy] = useState<boolean>(true);
  const [enableCopySell, setEnableCopySell] = useState<boolean>(true);
  const searchParams = useSearchParams();
  const { currentNetwork, networkConfig } = useNetwork();

  const decodedWalletName = decodeURIComponent(
    searchParams?.get("walletName") || ""
  );
  const decodedWalletAddress = decodeURIComponent(
    searchParams?.get("walletAddress") || ""
  );

  useEffect(() => {
    setWalletName(decodedWalletName);
    setTargetTokenAddress(decodedWalletAddress);
  }, [decodedWalletName, decodedWalletAddress]);

  const { pair } = React.useContext(RootPairContext) as {
    pair: TPair;
  };

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const res = await rf
          .getRequest("CopyTradeRequest")
          .getSettings(currentNetwork);
        if (res) {
          setSettingsBuy({
            maxBuy: res.limitSuiPerBuy,
            minLPCopy: res.targetLiquidityMin,
            minMCCopy: res.targetMarketCapMin,
            maxMCCopy: res.targetMarketCapMax,
          });
        }
      } catch (error) {
        console.error(error);
      }
    };

    fetchSettings().then();
  }, []);

  const onAddWallet = () => {
    dispatch(setIsShowModalAddWallet({ isShow: true }));
  };

  const { isPrivyUser, isTradingEnabled } = usePrivyTradingEnablement();

  const balanceWalletsSelected = useMemo(() => {
    return activeWallets.reduce((sum, obj) => sum + +obj?.balance, 0);
  }, [activeWallets]);

  const [buyAmount, setBuyAmount] = useState<any>("");
  const buyAmountRef = useRef<HTMLInputElement>(null);
  const handleFocusInputAmount = (value: string) => {
    const formattedAmount = formatNumberWithCommas(value);
    buyAmountRef.current?.setSelectionRange(
      formattedAmount.length,
      formattedAmount.length
    );
  };

  const [buyPercent, setBuyPercent] = useState<any>("");
  const buyPercentRef = useRef<HTMLInputElement>(null);
  const handleHideCopyBuy = () => {
    setEnableCopyBuy(!enableCopyBuy);
    setBuyAmount("");
    setBuyPercent("");
  };

  const [sellPercent, setSellPercent] = useState<any>("");
  const sellPercentRef = useRef<HTMLInputElement>(null);
  const handleHideCopySell = () => {
    setEnableCopySell(!enableCopySell);
    if (!enableCopySell) {
      setEnableAutoSell(enableCopySell);
    }
    setSellPercent("");
  };

  // Auto sell state
  const [isShowSettingAutoSell, setIsShowSettingAutoSell] =
    useState<boolean>(false);
  const [receiveToken, setReceiveToken] = useState<string | null>(null); // Do not used yet
  const [triggersOrder, setTriggersOrder] = useState<any[]>([
    {
      priceChangePercent: 10,
      sellPercent: 50,
    },
    {
      priceChangePercent: -10,
      sellPercent: 50,
    },
  ]);
  const [enableAutoSell, setEnableAutoSell] = useState<boolean>(false);

  const handleChangeWalletName = (e: any) => {
    if (e.target.value.length > 20) {
      return;
    }
    setWalletName(e.target.value);
  };
  const onShowSettings = () => {
    if (!accessToken) return;
    if (isTabletOrMobile) {
      setShowModalSettingsOrder(true);
      return;
    }
    setIsShowSettings(true);
  };

  const mustEnterAmount = useMemo(() => {
    if (enableCopyBuy) {
      if (copyBuy === EBuyStrategy.BUY_FIXED_AMOUNT) {
        return !buyAmount;
      }
      if (copyBuy === EBuyStrategy.BUY_PERCENT_AMOUNT) {
        return !buyPercent;
      }
      return false;
    }
    if (enableCopySell) {
      return !sellPercent;
    }
    return false;
  }, [
    buyAmount,
    buyPercent,
    copyBuy,
    enableCopyBuy,
    copySell,
    enableCopySell,
    sellPercent,
  ]);

  const getBuySettings = () => {
    if (!enableCopyBuy) return null;
    if (copyBuy === EBuyStrategy.BUY_EXACT_COPY_IGNORE_SETTINGS) {
      return {
        buyStrategy: EBuyStrategy.BUY_EXACT_COPY_IGNORE_SETTINGS,
      };
    }
    if (copyBuy === EBuyStrategy.BUY_PERCENT_AMOUNT) {
      return {
        buyStrategy: EBuyStrategy.BUY_PERCENT_AMOUNT,
        buyPercentAmount: +buyPercent,
      };
    }
    return {
      buyStrategy: EBuyStrategy.BUY_FIXED_AMOUNT,
      buyFixedAmount: +buyAmount,
    };
  };
  const getSellSettings = () => {
    if (!enableCopySell) return null;
    return {
      sellStrategy: ESellStrategy.SELL_PERCENT_AMOUNT,
      sellPercentAmount: +sellPercent,
    };
  };
  const getAutoSellSettings = () => {
    if (!enableAutoSell) {
      return null;
    }

    return {
      isActive: enableAutoSell,
      triggers: triggersOrder,
      receiveToken: networkConfig?.nativeTokenAddress.wrap!,
    };
  };

  const createCopyTrade = async () => {
    if (!targetTokenAddress) {
      toastError("Error", "Please enter target wallet address");
      return;
    }
    if (targetTokenAddress === activeWallets[0].address) {
      toastError(
        "Error",
        "Target wallet address cannot be the same as your wallet"
      );
      return;
    }
    if (mustEnterAmount) {
      toastError("Error", "Please enter amount");
      return;
    }

    if (!sellPercent && enableCopySell) {
      toastError("Error", "Please enter sell percent");
      return;
    }
    if (+sellPercent === 0 && enableCopySell) {
      toastError("Error", "Sell percent must be greater than 0");
      return;
    }

    if (activeWallets.length === 0) {
      toastError("Error", "Please select wallet");
      return;
    }

    if (isPrivyUser && !isTradingEnabled) {
      dispatch(setIsShowModalEnableTradingPrivy({ isShow: true }));
      return;
    }

    try {
      await rf.getRequest("CopyTradeRequest").createTarget(currentNetwork, {
        isActive: true,
        targetWalletAddress: targetTokenAddress,
        targetWalletName: walletName || targetTokenAddress.slice(-6),
        walletAddress: activeWallets[0].address,
        buySettings: getBuySettings(),
        sellSettings: getSellSettings(),
        autoSellSettings: getAutoSellSettings(),
      });
      setTargetTokenAddress("");
      setWalletName("");
      setBuyAmount("");
      setBuyPercent("");
      setSellPercent("");
      AppBroadcast.dispatch(BROADCAST_EVENTS.REFRESH_DATA_COPY_TRADE);
      toastSuccess("Success", "Create copy trade successfully");
    } catch (error: any) {
      if (error.message.includes("invalid wallet address")) {
        toastError("Error", "Please input valid wallet address");
      } else {
        toastError("Error", error.message || "Create copy trade failed");
      }
    }
  };

  const _renderContentCopySell = () => {
    switch (copySell) {
      case ESellStrategy.SELL_PERCENT_AMOUNT:
        return (
          <div className="flex flex-col gap-[8px]">
            <div className="text-neutral-alpha-400 text-[12px]">
              Customize the amount you want to sell (by percentage)
            </div>
            <div className="bg-neutral-beta-800 border-white-50 flex items-center gap-2 !rounded-[6px] border p-2 ">
              <div className="body-sm-regular-12 text-neutral-alpha-800 flex-shrink-0">
                Sell Percent
              </div>
              <NumericFormat
                getInputRef={sellPercentRef}
                value={sellPercent ?? ""}
                onFocus={() => handleFocusInputAmount(sellPercent)}
                allowLeadingZeros
                allowNegative={false}
                thousandSeparator=","
                className="body-md-semibold-14 w-full bg-transparent text-right outline-none"
                decimalScale={2}
                isAllowed={({ floatValue }) => {
                  if (floatValue && floatValue > 100) {
                    return false;
                  }
                  return true;
                }}
                onValueChange={({ floatValue }) => {
                  setSellPercent(floatValue?.toString());
                }}
              />

              <div className="text-neutral-alpha-500">%</div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  const _renderContentCopyBuy = () => {
    switch (copyBuy) {
      case EBuyStrategy.BUY_FIXED_AMOUNT:
        return (
          <div className="flex flex-col gap-[8px]">
            <div className="text-neutral-alpha-400 text-[12px]">
              Copy the buy order with your set amount (Up to the max buy limit)
            </div>
            <div className="bg-neutral-beta-800 border-white-50 flex items-center gap-2 rounded-[6px] border p-2">
              <div className="body-sm-regular-12 text-neutral-alpha-800">
                Amount
              </div>
              <NumericFormat
                getInputRef={buyAmountRef}
                value={buyAmount ?? ""}
                // autoFocus={!isMobile}
                onFocus={() => handleFocusInputAmount(buyAmount)}
                allowLeadingZeros
                allowNegative={false}
                thousandSeparator=","
                className="body-md-semibold-14 w-full bg-transparent text-right outline-none"
                decimalScale={8}
                onValueChange={({ floatValue }) => {
                  setBuyAmount(floatValue?.toString());
                }}
              />

              <div className="text-neutral-alpha-500">
                <AppLogoNetwork
                  network={currentNetwork}
                  className={`h-[14px] w-[14px]`}
                  isBase
                />
              </div>
            </div>
          </div>
        );
      case EBuyStrategy.BUY_EXACT_COPY_IGNORE_SETTINGS:
        return (
          <div className="flex flex-col gap-[8px]">
            <div className="text-neutral-alpha-400 text-[12px]">
              Copy the exact buy amount 1:1 with the target wallet (ignore
              advanced settings)
            </div>
          </div>
        );
      case EBuyStrategy.BUY_PERCENT_AMOUNT:
        return (
          <div className="flex flex-col gap-[8px]">
            <div className="text-neutral-alpha-400 text-[12px]">
              Buy a percentage that corresponds to the buy order of the target
              wallet
            </div>
            <div className="bg-neutral-beta-800 tablet:rounded-[0px] tablet:border-0 border-white-50 flex items-center gap-2 rounded-[4px] border p-2 ">
              <div className="body-sm-regular-12 text-neutral-alpha-800 flex-shrink-0">
                Buy Percent
              </div>
              <NumericFormat
                getInputRef={buyPercentRef}
                value={buyPercent ?? ""}
                onFocus={() => handleFocusInputAmount(buyPercent)}
                allowLeadingZeros
                allowNegative={false}
                thousandSeparator=","
                className="body-md-semibold-14 w-full bg-transparent text-right outline-none"
                decimalScale={2}
                isAllowed={({ floatValue }) => {
                  if (floatValue && floatValue > 100) {
                    return false;
                  }
                  return true;
                }}
                onValueChange={({ floatValue }) => {
                  setBuyPercent(floatValue?.toString());
                }}
              />

              <div className="text-neutral-alpha-500">%</div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  const _renderContent = () => {
    if (isShowSettings) {
      return (
        <SettingsForm
          setSettingsBuy={setSettingsBuy}
          settingsBuy={settingsBuy}
          // setSettingsSell={setSettingsSell}
          // settingsSell={settingsSell}
          onCloseSettings={() => setIsShowSettings(false)}
        />
      );
    }
    if (isShowSelectWallet) {
      return (
        <WalletSelection
          onCloseWalletSettings={() => setIsShowSelectWallet(false)}
        />
      );
    }

    return (
      <div className="flex h-full flex-col gap-[12px]">
        {/* SELECT WALLET */}
        {!!wallets.length ? (
          <div
            onClick={() => setIsShowSelectWallet(true)}
            className="bg-neutral-alpha-50 flex cursor-pointer items-center justify-between rounded-[4px] p-[4px]"
          >
            <div className="flex items-center gap-2">
              <div className="tablet:bg-neutral-beta-500 flex h-6 w-6 items-center justify-center rounded-[4px]">
                <WalletIcon />
              </div>
              <div className="flex gap-2">
                <div className="body-sm-semibold-12">Wallet</div>
                <div className="body-xs-regular-10 text-brand-500 bg-brand-800 border-brand-800 rounded-[80px] border px-[6px]">
                  {activeWallets.length} selected
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                <div className="body-sm-regular-12">
                  {formatNumber(balanceWalletsSelected)}
                </div>
                <div className="text-neutral-alpha-500">
                  <AppLogoNetwork
                    network={currentNetwork}
                    className={`h-[14px] w-[14px]`}
                    isBase
                  />
                </div>
              </div>

              <div>
                <ChevronDownIcon className="text-neutral-alpha-500 h-[16px] w-[16px] rotate-[-90deg]" />
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-neutral-alpha-50 flex items-center justify-between rounded-[4px] p-[4px]">
            <div className="flex items-center gap-2">
              <div className="bg-neutral-beta-500 flex h-6 w-6 items-center justify-center rounded-[4px]">
                <WalletIcon />
              </div>
              <div className="flex gap-2">
                <div className="body-sm-semibold-12">Wallet</div>
              </div>
            </div>
            {accessToken && (
              <div
                className="action-xs-medium-12 text-brand-500 cursor-pointer"
                onClick={onAddWallet}
              >
                Add Wallet
              </div>
            )}
          </div>
        )}
        {/* COPY WALLET */}
        <div className="rounded-4 bg-neutral-alpha-50 flex w-full flex-shrink-0 flex-col gap-[15px] p-[8px]">
          <div className="text-white-1000 text-sm font-semibold">
            COPY WALLET
          </div>
          <div className="border-white-50 bg-black-900 flex h-[34px] items-center gap-2 rounded border px-1 pl-1 pr-2">
            <div className="text-white-800 border-white-50 border-r pl-[2px] pr-1 text-[12px] font-normal leading-[18px]">
              Wallet address
            </div>
            <input
              value={targetTokenAddress}
              onChange={(e) => setTargetTokenAddress(e.target.value)}
              type="text"
              className="text-white-1000 placeholder:text-white-200 bg-black-900 flex-1 text-[12px] font-normal leading-[18px] focus:outline-none"
              placeholder="Add wallet address"
            />
          </div>

          <div className="border-white-50 bg-black-900 flex h-[34px] items-center gap-2 rounded border px-1 pl-1 pr-2">
            <div className="text-white-800 border-white-50 flex-shrink-0 border-r pl-[2px] pr-1 text-[12px] font-normal leading-[18px]">
              Wallet name
            </div>

            <input
              value={walletName}
              onChange={handleChangeWalletName}
              type="text"
              className="text-white-1000 placeholder:text-white-200 bg-black-900 flex-1 text-[12px] font-normal leading-[18px] focus:outline-none"
              placeholder="Add wallet name"
            />
          </div>
        </div>

        {/* COPY BUY */}
        <div className="rounded-4 bg-neutral-alpha-50 flex w-full flex-shrink-0 flex-col gap-[8px] p-[8px]">
          <div className="text-white-1000 flex justify-between text-sm font-semibold">
            COPY BUY
            <div className="text-white-1000 text-sm font-semibold">
              <AppToggle value={enableCopyBuy} onChange={handleHideCopyBuy} />
            </div>
          </div>
          {enableCopyBuy && (
            <div className="flex flex-col gap-[8px]">
              <div className="bg-black-500 rounded-4 flex items-center gap-[4px] p-[4px]">
                {MENU_COPY_BUY.map((item, index) => {
                  return (
                    <div
                      key={index}
                      className="flex w-1/3 items-center"
                      onClick={() => setCopyBuy(item.value)}
                    >
                      <div
                        className={`${
                          item.value === copyBuy
                            ? "text-white-1000 bg-white-100"
                            : "text-white-500"
                        } rounded-4 w-full cursor-pointer p-[8px] text-center text-[13px] font-[400]`}
                      >
                        {item.label}
                      </div>
                    </div>
                  );
                })}
              </div>
              {_renderContentCopyBuy()}
            </div>
          )}
        </div>

        {/* COPY SELL */}
        <div className="rounded-4 bg-neutral-alpha-50 flex w-full flex-shrink-0 flex-col gap-[8px] p-[8px]">
          <div className="text-white-1000 flex justify-between text-sm font-semibold">
            COPY SELL
            <div className="text-white-1000 text-sm font-semibold">
              <AppToggle value={enableCopySell} onChange={handleHideCopySell} />
            </div>
          </div>
          {enableCopySell && (
            <div className="flex flex-col gap-[8px]">
              {/* <div className="flex items-center gap-[4px] bg-black-500 p-[4px] rounded-4">
                {MENU_COPY_SELL.map((item, index) => {
                  return (
                    <div
                      key={index}
                      className="flex items-center w-full"
                      onClick={() => setCopySell(item.value)}
                    >
                      <div
                        className={`${item.value === copySell ? 'text-white-1000 bg-white-100' : 'text-white-500'} font-[400] text-center w-full cursor-pointer p-[8px] text-[13px] rounded-4`}
                      >
                        {item.label}
                      </div>
                    </div>
                  );
                })}
              </div> */}
              {_renderContentCopySell()}
            </div>
          )}
        </div>

        {/* BUTTON TRADE */}
        <FooterForm
          showCopyBuy={enableCopyBuy}
          showCopySell={enableCopySell}
          createOrder={createCopyTrade}
          balance={balanceWalletsSelected}
          amount={buyAmount}
          onShowSettings={onShowSettings}
          toggleSetAutoSell={() => {
            setEnableAutoSell(!enableAutoSell);
            if (!enableAutoSell) {
              setEnableCopySell(enableAutoSell);
              setIsShowSettingAutoSell(true);
            }
          }}
          autoSell={enableAutoSell}
          onShowSettingAutoSell={() => setIsShowSettingAutoSell(true)}
        />
      </div>
    );
  };

  return (
    <div
      className={clsx(
        "bg-neutral-alpha-50 tablet:pr-[8px] border-neutral-alpha-50 tablet:overflow-hidden flex h-full w-full overflow-auto border-b px-[8px] py-[12px]",
        isShowSelectWallet ? "pr-2" : "pr-2"
      )}
    >
      <div className="w-full">{_renderContent()}</div>

      {isShowModalWalletSelection && (
        <ModalBottomMobile
          isOpen={isShowModalWalletSelection}
          onClose={() => setShowModalWalletSelection(false)}
        >
          <div className="w-full p-[16px]">
            <WalletSelection
              onCloseWalletSettings={() => setIsShowSelectWallet(false)}
            />
          </div>
        </ModalBottomMobile>
      )}

      {isShowModalSettingsOrder && (
        <ModalBottomMobile
          isOpen={isShowModalSettingsOrder}
          onClose={() => setShowModalSettingsOrder(false)}
        >
          <div className="w-full p-[16px]">
            <SettingsForm
              settingsBuy={settingsBuy}
              setSettingsBuy={setSettingsBuy}
              // settingsSell={settingsSell}
              // setSettingsSell={setSettingsSell}
              onCloseSettings={() => setShowModalSettingsOrder(false)}
            />
          </div>
        </ModalBottomMobile>
      )}

      <ModalAutoSell
        pair={pair}
        onClose={() => setIsShowSettingAutoSell(false)}
        isOpen={isShowSettingAutoSell}
        setReceiveToken={setReceiveToken}
        receiveToken={receiveToken}
        setTriggersOrder={setTriggersOrder}
        triggersOrder={triggersOrder}
        isShowReceiveSui={false}
        isOrder={false}
      />
    </div>
  );
};
export default CopyTradingForm;
